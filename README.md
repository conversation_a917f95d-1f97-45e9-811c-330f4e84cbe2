# 数据处理脚本

这个脚本用于处理 geoStat.log 文件和 Excel 文件，生成新的包含 google 和 azure 数据的 Excel 文件。

## 安装 Node.js

由于系统中没有安装 Node.js，您需要先安装它：

### macOS 安装方法：

1. **使用 Homebrew（推荐）：**
   ```bash
   # 如果没有安装 Homebrew，先安装它
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # 安装 Node.js
   brew install node
   ```

2. **从官网下载：**
   - 访问 https://nodejs.org/
   - 下载 LTS 版本的 macOS 安装包
   - 运行安装包

3. **使用 nvm（Node Version Manager）：**
   ```bash
   # 安装 nvm
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   
   # 重启终端或运行
   source ~/.bashrc
   
   # 安装最新的 LTS Node.js
   nvm install --lts
   nvm use --lts
   ```

## 安装依赖

安装 Node.js 后，在项目目录中运行：

```bash
npm install
```

## 运行脚本

### 测试 geoStat.log 文件读取：
```bash
node test_geostat_read.js
```

### 运行完整的数据处理：
```bash
node test.js
```

## 文件说明

- `test.js` - 主要的数据处理脚本
- `test_geostat_read.js` - 测试 geoStat.log 文件读取的脚本
- `geoStat.log` - 输入的日志文件
- `/Users/<USER>/rni_daily_stats.xlsx` - 原始 Excel 文件（需要存在）
- `updated_rni_daily_stats.xlsx` - 输出的新 Excel 文件

## 数据格式

### geoStat.log 格式：
```javascript
[
  { _id: { src: 'BRE', day: '2025-07-01', engine: 'azure' }, count: 4 },
  { _id: { src: 'BRE', day: '2025-07-01', engine: 'google' }, count: 6 },
  // ...
]
```

### Excel 输入格式：
| Date | rni_TRB | rni_BRE | rni_DDF | rni_CAR | rni_EDM | rni_CLG |
|------|---------|---------|---------|---------|---------|---------|
| 2025-07-01 | 10 | 20 | 30 | 40 | 50 | 60 |

### Excel 输出格式：
| Date | rni_TRB | rni_BRE | ... | google_TRB | azure_TRB | google_BRE | azure_BRE | ... |
|------|---------|---------|-----|------------|-----------|------------|-----------|-----|
| 2025-07-01 | 10 | 20 | ... | 5 | 3 | 6 | 4 | ... |
