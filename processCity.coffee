moment = require 'moment-timezone'

utcDate = new Date('2025-05-24T19:00:00.000Z')
console.log('🚀 ~ processCity.coffee:4 ~ utcDate:', utcDate)

  # Set options for Edmonton time zone (America/Edmonton)
options =
  timeZone: "America/Edmonton"
  year: "numeric"
  month: "2-digit"
  day: "2-digit"
  hour: "2-digit"
  minute: "2-digit"
  hour12: false  # 24-hour format

# Format the date for Edmonton time zone
edmontonTime = new Intl.DateTimeFormat('en-CA', options).format(utcDate)
console.log('🚀 ~ processCity.coffee:17 ~ edmontonTime:', edmontonTime)

# zone = 'America/Los_Angeles'
# zone = 'America/Vancouver'
# # zone = 'America/Toronto'
# ret = convertUTCToLocalTime('2025-01-24T19:00:00.000Z', zone)
# console.log ret
