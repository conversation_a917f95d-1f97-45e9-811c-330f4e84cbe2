const fs = require('fs');
const XLSX = require('xlsx');

// 读取并解析 geoStat.log 文件
function readGeoStatLog(filePath) {
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        // 由于文件是JavaScript对象格式而不是标准JSON，需要使用eval来解析
        // 为了安全起见，先检查文件内容是否只包含数组和对象
        if (data.trim().startsWith('[') && data.trim().endsWith(']')) {
            // 使用Function构造函数来安全地执行代码
            const result = new Function('return ' + data)();
            return result;
        } else {
            throw new Error('文件格式不正确，应该是一个数组');
        }
    } catch (error) {
        console.error('读取 geoStat.log 文件失败:', error);
        return [];
    }
}

// 读取 Excel 文件
function readExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        return XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    } catch (error) {
        console.error('读取 Excel 文件失败:', error);
        return [];
    }
}

// 处理 geoStat.log 数据，按日期和src分组
function processGeoStatData(geoStatData) {
    const processedData = {};

    geoStatData.forEach(item => {
        const { src, day, engine } = item._id;
        const count = item.count;

        if (!processedData[day]) {
            processedData[day] = {};
        }

        if (!processedData[day][src]) {
            processedData[day][src] = {};
        }

        processedData[day][src][engine] = count;
    });

    return processedData;
}

// 生成新的 Excel 数据
function generateNewExcelData(originalExcelData, geoStatProcessedData) {
    if (originalExcelData.length === 0) {
        console.error('原始 Excel 数据为空');
        return [];
    }

    // 获取原始表头
    const originalHeaders = originalExcelData[0];
    const newHeaders = [...originalHeaders];

    // 为每个 src 添加 google 和 azure 列
    const srcColumns = originalHeaders.slice(1); // 去掉 Date 列
    srcColumns.forEach(srcColumn => {
        if (srcColumn.startsWith('rni_')) {
            const src = srcColumn.replace('rni_', '');
            newHeaders.push(`google_${src}`);
            newHeaders.push(`azure_${src}`);
        }
    });

    const newData = [newHeaders];

    // 处理数据行
    for (let i = 1; i < originalExcelData.length; i++) {
        const row = originalExcelData[i];
        const date = row[0];
        const newRow = [...row];

        // 为每个 src 添加对应的 google 和 azure 数据
        srcColumns.forEach(srcColumn => {
            if (srcColumn.startsWith('rni_')) {
                const src = srcColumn.replace('rni_', '');

                // 添加 google 数据
                const googleCount = geoStatProcessedData[date] &&
                                  geoStatProcessedData[date][src] &&
                                  geoStatProcessedData[date][src]['google']
                                  ? geoStatProcessedData[date][src]['google'] : 0;
                newRow.push(googleCount);

                // 添加 azure 数据
                const azureCount = geoStatProcessedData[date] &&
                                 geoStatProcessedData[date][src] &&
                                 geoStatProcessedData[date][src]['azure']
                                 ? geoStatProcessedData[date][src]['azure'] : 0;
                newRow.push(azureCount);
            }
        });

        newData.push(newRow);
    }

    return newData;
}

// 写入新的 Excel 文件
function writeExcelFile(data, outputPath) {
    try {
        const worksheet = XLSX.utils.aoa_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
        XLSX.writeFile(workbook, outputPath);
        console.log(`新的 Excel 文件已生成: ${outputPath}`);
    } catch (error) {
        console.error('写入 Excel 文件失败:', error);
    }
}

// 主函数
function main() {
    const geoStatLogPath = '/Users/<USER>/testCode/geoStat.log';
    const originalExcelPath = '/Users/<USER>/rni_daily_stats.xlsx';
    const outputExcelPath = '/Users/<USER>/testCode/updated_rni_daily_stats.xlsx';

    console.log('开始处理数据...');

    // 1. 读取 geoStat.log 文件
    console.log('读取 geoStat.log 文件...');
    const geoStatData = readGeoStatLog(geoStatLogPath);
    console.log(`geoStat.log 数据条数: ${geoStatData.length}`);

    // 2. 读取原始 Excel 文件
    console.log('读取原始 Excel 文件...');
    const originalExcelData = readExcelFile(originalExcelPath);
    console.log(`原始 Excel 数据行数: ${originalExcelData.length}`);

    // 3. 处理 geoStat 数据
    console.log('处理 geoStat 数据...');
    const processedGeoStatData = processGeoStatData(geoStatData);

    // 4. 生成新的 Excel 数据
    console.log('生成新的 Excel 数据...');
    const newExcelData = generateNewExcelData(originalExcelData, processedGeoStatData);

    // 5. 写入新的 Excel 文件
    console.log('写入新的 Excel 文件...');
    writeExcelFile(newExcelData, outputExcelPath);

    console.log('处理完成！');
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    readGeoStatLog,
    readExcelFile,
    processGeoStatData,
    generateNewExcelData,
    writeExcelFile,
    main
};